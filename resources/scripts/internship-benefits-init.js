document.addEventListener('DOMContentLoaded', function() {
    const benefitsSections = document.querySelectorAll('.internship-benefits-line');

    benefitsSections.forEach(section => {
        const benefitsContainer = section.querySelector('[data-benefits-container]');
        if (!benefitsContainer) return;

        const benefitItems = benefitsContainer.querySelectorAll('.benefit-line-item');
        const progressLines = benefitsContainer.querySelectorAll('.progress-line');
        const benefitContents = benefitsContainer.querySelectorAll('[data-benefit-content]');

        if (benefitItems.length === 0) return;

        let currentActiveIndex = 0;
        let progressTimeout = null;
        let progressInterval = null;

        // Calculate content height for proper animation
        function calculateContentHeight(content) {
            // Find the actual paragraph element inside the content
            const innerParagraph = content.querySelector('p');
            if (!innerParagraph) return 200; // fallback height

            // Create a clone of the inner content div (description-inner)
            const descriptionInner = content.querySelector('.description-inner');
            if (!descriptionInner) return 200; // fallback height

            const clone = descriptionInner.cloneNode(true);
            clone.style.position = 'absolute';
            clone.style.visibility = 'hidden';
            clone.style.top = '-9999px';
            clone.style.width = content.offsetWidth + 'px'; // Match the original width
            clone.style.maxHeight = 'none';
            clone.style.height = 'auto';

            content.parentNode.appendChild(clone);
            const height = clone.offsetHeight;
            content.parentNode.removeChild(clone);

            // Add some padding to ensure we don't cut off content
            return height + 10;
        }

        // Calculate and set progress line height for a specific item
        function calculateAndSetProgressLineHeight(itemIndex) {
            if (itemIndex >= progressLines.length) return;

            const benefitItem = benefitItems[itemIndex];
            const progressLine = progressLines[itemIndex];

            // Get the total height of the benefit item
            const itemHeight = benefitItem.offsetHeight;
            // Subtract 36px (icon height) and 12px (distance from icon) = 48px total
            const lineHeight = Math.max(57, itemHeight - 48); // Ensure minimum 57px

            progressLine.style.height = lineHeight + 'px';
        }

        // Set initial progress line heights (all items start closed except first)
        function setInitialProgressLineHeights() {
            benefitItems.forEach((_, index) => {
                if (index < progressLines.length) {
                    // For initial setup, calculate based on closed state (57px minimum)
                    // except for the first item which starts open
                    if (index === 0) {
                        calculateAndSetProgressLineHeight(index);
                    } else {
                        progressLines[index].style.height = '57px';
                    }
                }
            });
        }

        // Initialize: hide all content except first
        benefitContents.forEach((content, index) => {
            if (index === 0) {
                // First item starts open with calculated height
                const targetHeight = calculateContentHeight(content);
                content.style.maxHeight = targetHeight + 'px';
                content.style.opacity = '1';
            } else {
                // All other items start closed
                content.style.maxHeight = '0';
                content.style.opacity = '0';
            }
        });

        // Set initial progress line heights
        setInitialProgressLineHeights();

        // Reset all progress lines
        function resetAllProgress() {
            progressLines.forEach(line => {
                const progressFill = line.querySelector('.progress-fill');
                if (progressFill) {
                    progressFill.style.maxHeight = '0';
                    progressFill.style.transitionDuration = '0s';
                }
            });
        }

        // Show content for specific item with fade animation
        function showContent(index) {
            benefitContents.forEach((content, i) => {
                if (i === index) {
                    // Calculate the actual height needed
                    const targetHeight = calculateContentHeight(content);
                    // Fade in and expand the active content
                    content.style.opacity = '1';
                    content.style.maxHeight = targetHeight + 'px';
                } else {
                    // Fade out and collapse inactive content
                    content.style.opacity = '0';
                    content.style.maxHeight = '0';
                }
            });

            // Update progress line height for the currently active item
            setTimeout(() => {
                calculateAndSetProgressLineHeight(index);

                // Reset other progress lines to minimum height (57px) since their content is collapsed
                benefitItems.forEach((_, i) => {
                    if (i !== index && i < progressLines.length) {
                        progressLines[i].style.height = '57px';
                    }
                });
            }, 50); // Small delay to ensure content has finished transitioning
        }

        // Start progress animation for specific line
        function startProgress(index) {
            if (index >= progressLines.length) return;

            const progressLine = progressLines[index];
            const progressFill = progressLine.querySelector('.progress-fill');

            if (!progressFill) return;

            // Reset and start animation
            progressFill.style.transitionDuration = '0s';
            progressFill.style.maxHeight = '0';

            // Force reflow
            progressFill.offsetHeight;

            // Start animation
            progressFill.style.transitionDuration = '4s';
            progressFill.style.maxHeight = '100%';
        }

        // Stop all progress and timeouts
        function stopAllProgress() {
            if (progressTimeout) {
                clearTimeout(progressTimeout);
                progressTimeout = null;
            }
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
            resetAllProgress();
        }

        // Activate specific item
        function activateItem(index) {
            stopAllProgress();
            currentActiveIndex = index;
            showContent(index);

            // Start progress for current item if it has a progress line
            if (index < progressLines.length) {
                startProgress(index);

                // Set timeout to move to next item
                progressTimeout = setTimeout(() => {
                    const nextIndex = (index + 1) % benefitItems.length;
                    activateItem(nextIndex);
                }, 4000);
            }
        }

        // Add click handlers to benefit items
        benefitItems.forEach((item, index) => {
            item.addEventListener('click', function() {
                // If clicking the already active item, restart its progress
                if (index === currentActiveIndex) {
                    activateItem(index);
                } else {
                    // Activate the clicked item
                    activateItem(index);
                }
            });
        });

        // Start the automatic progression with the first item
        activateItem(0);
    });
});